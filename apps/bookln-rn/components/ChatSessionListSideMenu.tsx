import { useJglAiQAHistoryV2 } from '@jgl/ai-qa-v2';
import { useSafeAreaInsets } from '@jgl/biz-func';
import {
  JglGameButton,
  JglStateView,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { useWindowDimensions } from '@jgl/utils';
import type { ISession } from '@yunti-private/basic-im';
import { isPlatform } from '@yunti-private/platform-check';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { StyleSheet, TouchableWithoutFeedback, View } from 'react-native';
import {
  type GestureEvent,
  ScrollView as GestureScrollView,
  PanGestureHandler,
  type PanGestureHandlerEventPayload,
  State,
} from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

const menuWidthRatio = 0.85;

export type ChatSessionListSideMenuRef = {
  openMenu: () => void;
  closeMenu: () => void;
  toggleMenu: () => void;
};

type Props = {
  navigationBarHeight: number;
  onPressHistoryItem: (item: ISession) => void;
  onPressCreateNewChatSession: () => void;
};

/**
 * 左侧滑出侧边栏菜单组件
 * - 支持手势左滑关闭
 * - 遮罩层点击关闭
 * - 动画与遮罩透明度联动
 */
export const ChatSessionListSideMenu = forwardRef<
  ChatSessionListSideMenuRef,
  Props
>((props, ref) => {
  const {
    navigationBarHeight,
    onPressHistoryItem,
    onPressCreateNewChatSession,
  } = props;
  const safeInsets = useSafeAreaInsets();

  // 获取屏幕尺寸，适配横竖屏
  const windowSize = useWindowDimensions();
  const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = windowSize;
  const lastScreenWidthRef = useRef(SCREEN_WIDTH);
  // console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ windowSize:', JSON.stringify(windowSize));

  // 菜单宽度自适应屏幕
  const sideMenuWidth = useMemo(() => {
    const resultWidth = SCREEN_WIDTH * menuWidthRatio;
    return resultWidth;
  }, [SCREEN_WIDTH]);

  // console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - sideMenuWidth:', JSON.stringify(sideMenuWidth));

  // 动画相关状态
  const translateX = useSharedValue(-sideMenuWidth); // 菜单初始隐藏在左侧
  const isMenuOpen = useSharedValue(0); // 动画层菜单开关
  const [isOpen, setIsOpen] = useState(false); // React 层菜单开关（用于遮罩渲染）

  const { historyList, historyListApiState } = useJglAiQAHistoryV2();

  // console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - historyList:', JSON.stringify(historyList));

  // 菜单滑动动画样式
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  // 遮罩层动画样式，透明度与菜单滑动联动
  const maskAnimatedStyle = useAnimatedStyle(() => {
    const opacity = ((translateX.value + sideMenuWidth) / sideMenuWidth) * 0.5;
    return {
      opacity: Math.max(0, Math.min(0.4, opacity)),
      backgroundColor: '#000000',
      position: 'absolute',
      top: 0,
      left: 0,
      width: SCREEN_WIDTH,
      // height: SCREEN_HEIGHT,
      bottom: 0,
      zIndex: 1001,
    };
  });

  // 暴露控制方法给父组件
  useImperativeHandle(ref, () => ({
    openMenu,
    closeMenu,
    toggleMenu,
  }));

  /** 打开菜单（动画） */
  const openMenu = useCallback(() => {
    //   console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - openMenu');
    const newTranslateX = 0; // 左侧菜单完全展开时的位置
    //   console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ openMenu ~ newTranslateX:', newTranslateX);
    translateX.value = withSpring(newTranslateX, {
      stiffness: 100,
      damping: 20,
      mass: 0.25,
    });
    isMenuOpen.value = 1;
    setIsOpen(true);
  }, [isMenuOpen, translateX]);

  /** 关闭菜单（动画） */
  const closeMenu = useCallback(() => {
    //   console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - closeMenu');
    const newTranslateX = -sideMenuWidth; // 左侧菜单完全隐藏时的位置
    //   console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ closeMenu ~ newTranslateX:', newTranslateX);
    translateX.value = withSpring(newTranslateX, {
      stiffness: 100,
      damping: 20,
      mass: 0.25,
    });
    isMenuOpen.value = 0;
    setIsOpen(false);
  }, [isMenuOpen, translateX, sideMenuWidth]);

  useEffect(() => {
    if (lastScreenWidthRef.current !== SCREEN_WIDTH) {
      lastScreenWidthRef.current = SCREEN_WIDTH;
      closeMenu();
    }
  }, [SCREEN_WIDTH, closeMenu]);

  /** 切换菜单开关 */
  const toggleMenu = useCallback(() => {
    if (isMenuOpen.value === 1) {
      closeMenu();
    } else {
      openMenu();
    }
  }, [closeMenu, isMenuOpen.value, openMenu]);

  /**
   * 手势滑动菜单
   * - 以菜单展开位置为基准，累加 translationX
   * - 限制滑动范围在 [隐藏, 展开] 区间
   */
  const handlePan = useCallback(
    (event: GestureEvent<PanGestureHandlerEventPayload>) => {
      'worklet';
      const menuStartX = 0; // 左侧菜单展开时的起始位置
      translateX.value = menuStartX + event.nativeEvent.translationX;
      translateX.value = Math.max(
        -sideMenuWidth, // 最小值：完全隐藏
        Math.min(0, translateX.value), // 最大值：完全展开
      );
    },
    [sideMenuWidth, translateX],
  );

  /**
   * 手势结束时判断菜单收回或展开
   * - 超过一半则收回，否则保持展开
   * - 同步动画层和 React 层状态
   */
  const handleStateChange = useCallback(
    (state: State) => {
      'worklet';
      if (state === State.END) {
        if (translateX.value > -sideMenuWidth / 2) {
          // 如果滑动超过一半，则完全展开
          translateX.value = withSpring(0, {
            stiffness: 100,
            damping: 20,
          });
          runOnJS(() => {
            isMenuOpen.value = 1;
            setIsOpen(true);
          })();
        } else {
          // 否则完全收回
          translateX.value = withSpring(-sideMenuWidth, {
            stiffness: 100,
            damping: 20,
          });
          runOnJS(() => {
            isMenuOpen.value = 0;
            setIsOpen(false);
          })();
        }
      }
    },
    [isMenuOpen, sideMenuWidth, translateX],
  );

  const scrollViewRef = useRef<GestureScrollView>(null);

  const renderHistoryList = useMemo(() => {
    return (
      <GestureScrollView
        ref={scrollViewRef}
        style={{ flex: 1 }}
        contentContainerStyle={{
          paddingBottom:
            navigationBarHeight + safeInsets.bottom + safeInsets.top,
        }}
      >
        {historyList.map((item, index) => {
          if (item.type === 'title') {
            return (
              <JglText
                mt={index !== 0 ? 16 : 0}
                mb={12}
                color='$color9'
                fontSize={'$4'}
                key={item.name}
              >
                {item.name}
              </JglText>
            );
          } else {
            return (
              <JglTouchable
                key={item.data.id}
                mb={12}
                justifyContent='flex-start'
                minHeight={24}
                onPress={() => onPressHistoryItem(item.data)}
              >
                <JglText
                  color='$color12'
                  maxLines={1}
                  fontSize={16}
                  lineHeight={24}
                  key={item.data.id}
                >
                  {item.data.title}
                </JglText>
              </JglTouchable>
            );
          }
        })}
      </GestureScrollView>
    );
  }, [
    historyList,
    navigationBarHeight,
    onPressHistoryItem,
    safeInsets.bottom,
    safeInsets.top,
  ]);

  const renderCreateNewChatSessionButton = useMemo(() => {
    return (
      <JglGameButton
        backgroundColor='white'
        secondaryBgColor='#7191FF'
        h={48}
        m={16}
        radius={10}
        onPress={onPressCreateNewChatSession}
      >
        <JglXStack h={52} alignItems='center' justifyContent='center' space={4}>
          <JglText fontSize={16} fontWeight='500' color='#7191FF'>
            创建新对话
          </JglText>
        </JglXStack>
      </JglGameButton>
    );
  }, [onPressCreateNewChatSession]);

  return (
    <>
      {/* 遮罩层，菜单打开时显示，点击关闭 */}
      <Animated.View
        style={maskAnimatedStyle}
        pointerEvents={isOpen ? 'auto' : 'none'}
      >
        {isOpen && (
          <TouchableWithoutFeedback onPress={closeMenu}>
            <View style={{ flex: 1 }} />
          </TouchableWithoutFeedback>
        )}
      </Animated.View>
      {/* 侧边栏菜单，支持手势左滑关闭 */}
      <PanGestureHandler
        simultaneousHandlers={scrollViewRef}
        onGestureEvent={handlePan}
        onHandlerStateChange={({ nativeEvent }) =>
          handleStateChange(nativeEvent.state)
        }
      >
        <Animated.View
          style={[
            styles.sideMenuContainer,
            {
              top: 0,
              width: sideMenuWidth,
              height:
                SCREEN_HEIGHT +
                (isPlatform({ os: 'android' }).isMatch
                  ? safeInsets.top * 2
                  : safeInsets.top),
            },
            animatedStyle,
            { zIndex: 1002 },
          ]}
        >
          <JglYStack
            flex={1}
            pt={navigationBarHeight}
            pb={safeInsets.bottom + 48}
          >
            <JglStateView
              width={sideMenuWidth}
              px={16}
              isLoading={historyListApiState === 'loading'}
              isEmpty={historyList.length === 0}
            >
              {renderHistoryList}
            </JglStateView>
            {renderCreateNewChatSessionButton}
          </JglYStack>
        </Animated.View>
      </PanGestureHandler>
    </>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sideMenuContainer: {
    position: 'absolute',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuContent: {
    flex: 1,
  },
});
